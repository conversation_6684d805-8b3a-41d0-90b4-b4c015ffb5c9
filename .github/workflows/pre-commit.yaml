name: Pre-commit checks

on:
  pull_request:
    branches:
      - '**'
  push:
    branches:
      - '**'

jobs:
  pre-commit-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Source Code
        uses: actions/checkout@v4
      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Install pre-commit and tools
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit black==23.1.0 isort==5.12.0 autoflake==2.0.1
      - name: Run pre-commit hooks
        run: pre-commit run --all-files
