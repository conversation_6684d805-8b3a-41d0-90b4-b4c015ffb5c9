name: Build and upload Python package

on:
  workflow_dispatch:
  release:
    types: [created, published]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install setuptools wheel twine
      - name: Set package version
        run: |
          export VERSION="${GITHUB_REF#refs/tags/v}"
          sed -i "s/version=.*/version=\"${VERSION}\",/" setup.py
      - name: Build and publish
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
        run: |
          python setup.py bdist_wheel sdist
          twine upload dist/*
